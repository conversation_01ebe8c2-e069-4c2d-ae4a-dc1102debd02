@echo off
echo 🚀 Installing RepoSensei Dependencies...
echo.

echo 📦 Installing Frontend Dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Frontend installation failed!
    pause
    exit /b 1
)

echo.
echo 📦 Installing Backend Dependencies...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ❌ Backend installation failed!
    pause
    exit /b 1
)

cd ..
echo.
echo ✅ Installation completed successfully!
echo.
echo 🎯 To start development:
echo   1. Backend: cd backend && npm run dev
echo   2. Frontend: npm run dev
echo.
echo 🌐 Access the app at: http://localhost:5173
echo 📡 Backend API at: http://localhost:3001
echo.
pause
