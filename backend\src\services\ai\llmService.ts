import { HfInference } from '@huggingface/inference';
import axios from 'axios';
import { logger, logError, logPerformance } from '@/utils/logger';
import { redis } from '@/config/redis';

// Types
interface UserProfile {
  user: {
    login: string;
    name: string | null;
    bio: string | null;
  };
  repos: Array<{
    name: string;
    description: string | null;
    language: string | null;
    stargazers_count: number;
    forks_count: number;
    topics?: string[];
  }>;
}

interface AnimeBadge {
  characterName: string;
  anime: 'Naruto' | 'Demon Slayer';
  reason: string;
  badgeColor: string;
}

interface LLMResponse {
  success: boolean;
  data?: AnimeBadge;
  error?: string;
  source: 'ollama' | 'huggingface' | 'cache';
}

class LLMService {
  private hf: HfInference | null = null;
  private ollamaHost: string;
  private hfModel: string;
  private ollamaModel: string;

  constructor() {
    // Initialize Hugging Face client if API key is provided
    if (process.env.HUGGINGFACE_API_KEY) {
      this.hf = new HfInference(process.env.HUGGINGFACE_API_KEY);
    }

    this.ollamaHost = process.env.OLLAMA_HOST || 'http://localhost:11434';
    this.hfModel = process.env.HUGGINGFACE_MODEL_TEXT || 'meta-llama/Llama-2-7b-chat-hf';
    this.ollamaModel = process.env.OLLAMA_MODEL || 'llama3.1:8b';
  }

  /**
   * Generate a comprehensive prompt for character analysis
   */
  private generatePrompt(profile: UserProfile): string {
    const repoSummary = profile.repos.map(repo => ({
      name: repo.name,
      description: repo.description,
      language: repo.language,
      stars: repo.stargazers_count,
      forks: repo.forks_count,
      topics: repo.topics?.slice(0, 3) || []
    }));

    const userSummary = {
      name: profile.user.name,
      login: profile.user.login,
      bio: profile.user.bio,
      top_repos: repoSummary
    };

    return `You are an expert GitHub profile analyst with deep knowledge of anime, specifically Naruto and Demon Slayer. Analyze the GitHub user's profile and assign them a character that best represents their coding style and persona.

Analysis Framework:
- **Coding Style**: What do their repositories reveal about their approach?
- **Impact Level**: Are they influential (Kage/Hashira level) or specialized ninjas?
- **Primary Languages**: What do their language choices say about them?
- **Project Themes**: Do they build tools, UIs, systems, or research?
- **Personality**: What does their bio and project descriptions suggest?

Character Selection Criteria:
- Choose from Naruto or Demon Slayer characters only
- Match personality traits and abilities to coding patterns
- Consider both technical skills and leadership/impact
- Be creative but logical in your reasoning

User Profile Data:
${JSON.stringify(userSummary, null, 2)}

Respond with ONLY a valid JSON object in this exact format:
{
  "characterName": "Character Name",
  "anime": "Naruto" or "Demon Slayer",
  "reason": "2-3 sentence explanation connecting their coding style to the character",
  "badgeColor": "#HEXCOLOR"
}`;
  }

  /**
   * Try to get analysis from cache first
   */
  private async getCachedAnalysis(profileKey: string): Promise<AnimeBadge | null> {
    try {
      const cached = await redis.getJSON<AnimeBadge>(`analysis:${profileKey}`);
      if (cached) {
        logger.info('Retrieved analysis from cache', { profileKey });
        return cached;
      }
    } catch (error) {
      logError('Cache retrieval error', error as Error, { profileKey });
    }
    return null;
  }

  /**
   * Cache the analysis result
   */
  private async cacheAnalysis(profileKey: string, analysis: AnimeBadge): Promise<void> {
    try {
      // Cache for 24 hours
      await redis.setJSON(`analysis:${profileKey}`, analysis, 86400);
      logger.info('Cached analysis result', { profileKey });
    } catch (error) {
      logError('Cache storage error', error as Error, { profileKey });
    }
  }

  /**
   * Analyze profile using Ollama (local LLM)
   */
  private async analyzeWithOllama(prompt: string): Promise<LLMResponse> {
    const startTime = Date.now();
    
    try {
      const response = await axios.post(`${this.ollamaHost}/api/generate`, {
        model: this.ollamaModel,
        prompt,
        stream: false,
        options: {
          temperature: 0.7,
          top_p: 0.9,
          max_tokens: 500
        }
      }, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      logPerformance('Ollama LLM Analysis', startTime);

      if (response.data && response.data.response) {
        const result = this.parseResponse(response.data.response);
        if (result.success) {
          return { ...result, source: 'ollama' };
        }
      }

      return {
        success: false,
        error: 'Invalid response format from Ollama',
        source: 'ollama'
      };

    } catch (error) {
      logError('Ollama analysis failed', error as Error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ollama service unavailable',
        source: 'ollama'
      };
    }
  }

  /**
   * Analyze profile using Hugging Face
   */
  private async analyzeWithHuggingFace(prompt: string): Promise<LLMResponse> {
    const startTime = Date.now();

    if (!this.hf) {
      return {
        success: false,
        error: 'Hugging Face API key not configured',
        source: 'huggingface'
      };
    }

    try {
      const response = await this.hf.textGeneration({
        model: this.hfModel,
        inputs: prompt,
        parameters: {
          max_new_tokens: 500,
          temperature: 0.7,
          top_p: 0.9,
          return_full_text: false
        }
      });

      logPerformance('Hugging Face LLM Analysis', startTime);

      if (response.generated_text) {
        const result = this.parseResponse(response.generated_text);
        if (result.success) {
          return { ...result, source: 'huggingface' };
        }
      }

      return {
        success: false,
        error: 'Invalid response format from Hugging Face',
        source: 'huggingface'
      };

    } catch (error) {
      logError('Hugging Face analysis failed', error as Error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Hugging Face service unavailable',
        source: 'huggingface'
      };
    }
  }

  /**
   * Parse and validate LLM response
   */
  private parseResponse(responseText: string): { success: boolean; data?: AnimeBadge; error?: string } {
    try {
      // Clean up the response text
      let cleanText = responseText.trim();
      
      // Remove markdown code blocks if present
      const codeBlockRegex = /```(?:json)?\s*\n?(.*?)\n?\s*```/s;
      const match = cleanText.match(codeBlockRegex);
      if (match && match[1]) {
        cleanText = match[1].trim();
      }

      // Try to find JSON object in the text
      const jsonMatch = cleanText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanText = jsonMatch[0];
      }

      const parsed = JSON.parse(cleanText) as AnimeBadge;

      // Validate required fields
      if (!parsed.characterName || !parsed.anime || !parsed.reason || !parsed.badgeColor) {
        return {
          success: false,
          error: 'Missing required fields in AI response'
        };
      }

      // Validate anime field
      if (!['Naruto', 'Demon Slayer'].includes(parsed.anime)) {
        return {
          success: false,
          error: 'Invalid anime selection'
        };
      }

      // Validate color format
      if (!/^#[0-9A-Fa-f]{6}$/.test(parsed.badgeColor)) {
        return {
          success: false,
          error: 'Invalid color format'
        };
      }

      return { success: true, data: parsed };

    } catch (error) {
      return {
        success: false,
        error: `Failed to parse AI response: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Main method to analyze GitHub profile
   */
  public async analyzeProfile(profile: UserProfile): Promise<AnimeBadge> {
    const profileKey = `${profile.user.login}_${Date.now().toString().slice(-6)}`;
    
    // Check cache first
    const cached = await this.getCachedAnalysis(profile.user.login);
    if (cached) {
      return cached;
    }

    const prompt = this.generatePrompt(profile);
    let result: LLMResponse;

    // Try Ollama first (free and fast)
    result = await this.analyzeWithOllama(prompt);
    
    // Fallback to Hugging Face if Ollama fails
    if (!result.success && this.hf) {
      logger.info('Falling back to Hugging Face', { reason: result.error });
      result = await this.analyzeWithHuggingFace(prompt);
    }

    // If both fail, provide a fallback response
    if (!result.success) {
      logger.warn('All LLM services failed, using fallback', { 
        ollamaError: result.error,
        profile: profile.user.login 
      });
      
      result = {
        success: true,
        data: this.getFallbackAnalysis(profile),
        source: 'ollama'
      };
    }

    if (result.data) {
      // Cache the successful result
      await this.cacheAnalysis(profile.user.login, result.data);
      
      logger.info('Profile analysis completed', {
        username: profile.user.login,
        character: result.data.characterName,
        source: result.source
      });

      return result.data;
    }

    throw new Error('Failed to analyze profile with all available services');
  }

  /**
   * Fallback analysis when all AI services fail
   */
  private getFallbackAnalysis(profile: UserProfile): AnimeBadge {
    const languages = profile.repos
      .map(repo => repo.language)
      .filter(lang => lang !== null);
    
    const totalStars = profile.repos.reduce((sum, repo) => sum + repo.stargazers_count, 0);
    
    // Simple rule-based fallback
    if (languages.includes('JavaScript') || languages.includes('TypeScript')) {
      return {
        characterName: 'Naruto Uzumaki',
        anime: 'Naruto',
        reason: 'Like Naruto, you bring energy and versatility to your projects with JavaScript/TypeScript, always finding creative solutions.',
        badgeColor: '#FF7F00'
      };
    }
    
    if (languages.includes('Python')) {
      return {
        characterName: 'Tanjiro Kamado',
        anime: 'Demon Slayer',
        reason: 'Your Python skills show the same adaptability and problem-solving nature as Tanjiro, tackling challenges with determination.',
        badgeColor: '#107C80'
      };
    }
    
    if (totalStars > 100) {
      return {
        characterName: 'Kakashi Hatake',
        anime: 'Naruto',
        reason: 'Your high-starred repositories demonstrate the wisdom and technical mastery of the Copy Ninja himself.',
        badgeColor: '#708090'
      };
    }

    // Default fallback
    return {
      characterName: 'Zenitsu Agatsuma',
      anime: 'Demon Slayer',
      reason: 'Like Zenitsu, you may seem humble, but your code shows hidden potential and unique strengths waiting to be unleashed.',
      badgeColor: '#FFD700'
    };
  }

  /**
   * Health check for LLM services
   */
  public async healthCheck(): Promise<{ ollama: boolean; huggingface: boolean }> {
    const checks = await Promise.allSettled([
      // Check Ollama
      axios.get(`${this.ollamaHost}/api/tags`, { timeout: 5000 }),
      // Check Hugging Face (if configured)
      this.hf ? this.hf.textGeneration({
        model: this.hfModel,
        inputs: 'test',
        parameters: { max_new_tokens: 1 }
      }) : Promise.reject('Not configured')
    ]);

    return {
      ollama: checks[0]?.status === 'fulfilled',
      huggingface: checks[1]?.status === 'fulfilled'
    };
  }
}

// Export singleton instance
export const llmService = new LLMService();
export default llmService;
