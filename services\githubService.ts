
import axios from 'axios';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, GithubRepo, UserProfile } from '../types';

// Use backend API instead of direct GitHub API calls
const API_BASE = import.meta.env.VITE_API_URL || 'http://localhost:3001/api/v1';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for auth token
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Add response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear invalid token
      localStorage.removeItem('auth_token');
      // Optionally redirect to login
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

export const getUserProfileData = async (username: string): Promise<UserProfile> => {
  try {
    // Validate username format
    if (!username || typeof username !== 'string' || username.trim().length === 0) {
      throw new Error('Invalid GitHub username provided');
    }

    const cleanUsername = username.trim().toLowerCase();

    // Use backend API for GitHub data fetching
    const response = await apiClient.get(`/github/profile/${cleanUsername}`);

    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to fetch GitHub profile');
    }

    return response.data.data;

  } catch (error: any) {
    // Handle specific error types
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.error || error.message;

      switch (status) {
        case 404:
          throw new Error(`GitHub user not found: ${username}`);
        case 429:
          throw new Error('Rate limit exceeded. Please try again later.');
        case 403:
          throw new Error('GitHub API access forbidden. Please try again later.');
        case 500:
          throw new Error('Server error. Please try again later.');
        default:
          throw new Error(message || 'Failed to fetch GitHub profile');
      }
    }

    // Network or other errors
    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timeout. Please check your connection and try again.');
    }

    throw new Error(error.message || 'Failed to fetch GitHub profile');
  }
};

export const validateUsername = async (username: string): Promise<boolean> => {
  try {
    const response = await apiClient.get(`/github/validate/${username}`);
    return response.data.valid;
  } catch (error) {
    return false;
  }
};

export const searchUsers = async (query: string, limit = 10): Promise<{ login: string; avatar_url: string }[]> => {
  try {
    const response = await apiClient.get('/github/search/users', {
      params: { q: query, limit }
    });
    return response.data.users || [];
  } catch (error) {
    return [];
  }
};
