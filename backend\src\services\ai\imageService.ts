import { HfInference } from '@huggingface/inference';
import axios from 'axios';
import sharp from 'sharp';
import { logger, logError, logPerformance } from '@/utils/logger';
import { redis } from '@/config/redis';

interface AnimeBadge {
  characterName: string;
  anime: 'Naruto' | 'Demon Slayer';
  reason: string;
  badgeColor: string;
}

interface ImageGenerationResult {
  success: boolean;
  imageUrl?: string;
  error?: string;
  source: 'stable-diffusion' | 'huggingface' | 'unsplash' | 'fallback';
}

class ImageService {
  private hf: HfInference | null = null;
  private stableDiffusionUrl: string;
  private unsplashAccessKey: string | null;
  private hfImageModel: string;

  constructor() {
    // Initialize Hugging Face client
    if (process.env.HUGGINGFACE_API_KEY) {
      this.hf = new HfInference(process.env.HUGGINGFACE_API_KEY);
    }

    this.stableDiffusionUrl = process.env.STABLE_DIFFUSION_API_URL || 'http://localhost:7860';
    this.unsplashAccessKey = process.env.UNSPLASH_ACCESS_KEY || null;
    this.hfImageModel = process.env.HUGGINGFACE_MODEL_IMAGE || 'stabilityai/stable-diffusion-xl-base-1.0';
  }

  /**
   * Generate optimized prompt for character avatar
   */
  private generateImagePrompt(badge: AnimeBadge): string {
    const basePrompt = `Professional anime-style avatar of ${badge.characterName} from ${badge.anime}`;
    
    const stylePrompts = {
      'Naruto': 'ninja headband, orange and blue colors, dynamic pose, Konoha village background',
      'Demon Slayer': 'demon slayer uniform, traditional Japanese elements, sword, Taisho era aesthetic'
    };

    const qualityPrompts = [
      'high quality',
      'detailed face',
      'clean art style',
      'professional illustration',
      'vibrant colors',
      'studio lighting',
      'masterpiece',
      '4k resolution'
    ];

    const negativePrompts = [
      'blurry',
      'low quality',
      'distorted',
      'ugly',
      'duplicate',
      'morbid',
      'mutilated',
      'extra fingers',
      'poorly drawn hands',
      'poorly drawn face'
    ];

    return `${basePrompt}, ${stylePrompts[badge.anime]}, circular frame, ${badge.badgeColor} accent colors, ${qualityPrompts.join(', ')}. Negative prompt: ${negativePrompts.join(', ')}`;
  }

  /**
   * Check cache for existing image
   */
  private async getCachedImage(cacheKey: string): Promise<string | null> {
    try {
      const cached = await redis.get(`image:${cacheKey}`);
      if (cached) {
        logger.info('Retrieved image from cache', { cacheKey });
        return cached;
      }
    } catch (error) {
      logError('Image cache retrieval error', error as Error, { cacheKey });
    }
    return null;
  }

  /**
   * Cache generated image
   */
  private async cacheImage(cacheKey: string, imageUrl: string): Promise<void> {
    try {
      // Cache for 7 days
      await redis.set(`image:${cacheKey}`, imageUrl, 604800);
      logger.info('Cached generated image', { cacheKey });
    } catch (error) {
      logError('Image cache storage error', error as Error, { cacheKey });
    }
  }

  /**
   * Generate image using local Stable Diffusion
   */
  private async generateWithStableDiffusion(prompt: string): Promise<ImageGenerationResult> {
    const startTime = Date.now();

    try {
      const response = await axios.post(`${this.stableDiffusionUrl}/sdapi/v1/txt2img`, {
        prompt,
        negative_prompt: 'blurry, low quality, distorted, ugly, duplicate, morbid, mutilated',
        width: 512,
        height: 512,
        steps: 20,
        cfg_scale: 7,
        sampler_name: 'DPM++ 2M Karras',
        batch_size: 1,
        n_iter: 1,
        seed: -1,
        restore_faces: true,
        tiling: false,
        do_not_save_samples: true,
        do_not_save_grid: true
      }, {
        timeout: 60000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      logPerformance('Stable Diffusion Image Generation', startTime);

      if (response.data && response.data.images && response.data.images.length > 0) {
        const base64Image = response.data.images[0];
        const imageUrl = `data:image/png;base64,${base64Image}`;
        
        return {
          success: true,
          imageUrl,
          source: 'stable-diffusion'
        };
      }

      return {
        success: false,
        error: 'No image generated by Stable Diffusion',
        source: 'stable-diffusion'
      };

    } catch (error) {
      logError('Stable Diffusion generation failed', error as Error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Stable Diffusion service unavailable',
        source: 'stable-diffusion'
      };
    }
  }

  /**
   * Generate image using Hugging Face
   */
  private async generateWithHuggingFace(prompt: string): Promise<ImageGenerationResult> {
    const startTime = Date.now();

    if (!this.hf) {
      return {
        success: false,
        error: 'Hugging Face API key not configured',
        source: 'huggingface'
      };
    }

    try {
      const response = await this.hf.textToImage({
        model: this.hfImageModel,
        inputs: prompt,
        parameters: {
          width: 512,
          height: 512,
          num_inference_steps: 20,
          guidance_scale: 7.5
        }
      });

      logPerformance('Hugging Face Image Generation', startTime);

      if (response) {
        // Convert blob to base64
        const arrayBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        const base64Image = buffer.toString('base64');
        const imageUrl = `data:image/jpeg;base64,${base64Image}`;

        return {
          success: true,
          imageUrl,
          source: 'huggingface'
        };
      }

      return {
        success: false,
        error: 'No image generated by Hugging Face',
        source: 'huggingface'
      };

    } catch (error) {
      logError('Hugging Face image generation failed', error as Error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Hugging Face service unavailable',
        source: 'huggingface'
      };
    }
  }

  /**
   * Get fallback image from Unsplash
   */
  private async getFallbackFromUnsplash(badge: AnimeBadge): Promise<ImageGenerationResult> {
    if (!this.unsplashAccessKey) {
      return {
        success: false,
        error: 'Unsplash access key not configured',
        source: 'unsplash'
      };
    }

    try {
      const searchQuery = `${badge.anime.toLowerCase().replace(' ', '-')}-anime-character`;
      const response = await axios.get(`https://api.unsplash.com/search/photos`, {
        params: {
          query: searchQuery,
          per_page: 1,
          orientation: 'squarish'
        },
        headers: {
          'Authorization': `Client-ID ${this.unsplashAccessKey}`
        },
        timeout: 10000
      });

      if (response.data.results && response.data.results.length > 0) {
        const imageUrl = response.data.results[0].urls.small;
        
        return {
          success: true,
          imageUrl,
          source: 'unsplash'
        };
      }

      return {
        success: false,
        error: 'No suitable images found on Unsplash',
        source: 'unsplash'
      };

    } catch (error) {
      logError('Unsplash fallback failed', error as Error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unsplash service unavailable',
        source: 'unsplash'
      };
    }
  }

  /**
   * Generate default fallback avatar
   */
  private async generateFallbackAvatar(badge: AnimeBadge): Promise<ImageGenerationResult> {
    try {
      // Create a simple colored circle with character initial
      const initial = badge.characterName.charAt(0).toUpperCase();
      const size = 512;
      
      const svg = `
        <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
          <circle cx="${size/2}" cy="${size/2}" r="${size/2-10}" fill="${badge.badgeColor}" stroke="#ffffff" stroke-width="4"/>
          <text x="${size/2}" y="${size/2+20}" font-family="Arial, sans-serif" font-size="200" font-weight="bold" text-anchor="middle" fill="#ffffff">${initial}</text>
        </svg>
      `;

      const buffer = await sharp(Buffer.from(svg))
        .png()
        .toBuffer();

      const base64Image = buffer.toString('base64');
      const imageUrl = `data:image/png;base64,${base64Image}`;

      return {
        success: true,
        imageUrl,
        source: 'fallback'
      };

    } catch (error) {
      logError('Fallback avatar generation failed', error as Error);
      return {
        success: false,
        error: 'Failed to generate fallback avatar',
        source: 'fallback'
      };
    }
  }

  /**
   * Optimize image for web use
   */
  private async optimizeImage(imageUrl: string): Promise<string> {
    try {
      if (imageUrl.startsWith('data:image/')) {
        // Extract base64 data
        const base64Data = imageUrl.split(',')[1];
        const buffer = Buffer.from(base64Data, 'base64');

        // Optimize with sharp
        const optimizedBuffer = await sharp(buffer)
          .resize(400, 400, { fit: 'cover' })
          .jpeg({ quality: 85, progressive: true })
          .toBuffer();

        return `data:image/jpeg;base64,${optimizedBuffer.toString('base64')}`;
      }

      return imageUrl;
    } catch (error) {
      logError('Image optimization failed', error as Error);
      return imageUrl; // Return original if optimization fails
    }
  }

  /**
   * Main method to generate character avatar
   */
  public async generateAvatar(badge: AnimeBadge): Promise<string> {
    const cacheKey = `${badge.characterName}_${badge.anime}_${badge.badgeColor}`.replace(/\s+/g, '_');
    
    // Check cache first
    const cached = await this.getCachedImage(cacheKey);
    if (cached) {
      return cached;
    }

    const prompt = this.generateImagePrompt(badge);
    let result: ImageGenerationResult;

    // Try Stable Diffusion first (best quality, free if self-hosted)
    result = await this.generateWithStableDiffusion(prompt);

    // Fallback to Hugging Face if Stable Diffusion fails
    if (!result.success && this.hf) {
      logger.info('Falling back to Hugging Face for image generation', { reason: result.error });
      result = await this.generateWithHuggingFace(prompt);
    }

    // Fallback to Unsplash if AI generation fails
    if (!result.success && this.unsplashAccessKey) {
      logger.info('Falling back to Unsplash for image', { reason: result.error });
      result = await this.getFallbackFromUnsplash(badge);
    }

    // Final fallback to generated avatar
    if (!result.success) {
      logger.warn('All image services failed, using generated fallback', { 
        character: badge.characterName,
        error: result.error 
      });
      result = await this.generateFallbackAvatar(badge);
    }

    if (result.success && result.imageUrl) {
      // Optimize the image
      const optimizedImage = await this.optimizeImage(result.imageUrl);
      
      // Cache the result
      await this.cacheImage(cacheKey, optimizedImage);
      
      logger.info('Avatar generation completed', {
        character: badge.characterName,
        source: result.source
      });

      return optimizedImage;
    }

    throw new Error('Failed to generate avatar with all available services');
  }

  /**
   * Health check for image generation services
   */
  public async healthCheck(): Promise<{ 
    stableDiffusion: boolean; 
    huggingface: boolean; 
    unsplash: boolean 
  }> {
    const checks = await Promise.allSettled([
      // Check Stable Diffusion
      axios.get(`${this.stableDiffusionUrl}/sdapi/v1/options`, { timeout: 5000 }),
      // Check Hugging Face
      this.hf ? Promise.resolve(true) : Promise.reject('Not configured'),
      // Check Unsplash
      this.unsplashAccessKey ? 
        axios.get('https://api.unsplash.com/stats/total', {
          headers: { 'Authorization': `Client-ID ${this.unsplashAccessKey}` },
          timeout: 5000
        }) : Promise.reject('Not configured')
    ]);

    return {
      stableDiffusion: checks[0]?.status === 'fulfilled',
      huggingface: checks[1]?.status === 'fulfilled',
      unsplash: checks[2]?.status === 'fulfilled'
    };
  }
}

// Export singleton instance
export const imageService = new ImageService();
export default imageService;
