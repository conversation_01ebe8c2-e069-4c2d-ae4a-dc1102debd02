# Frontend Environment Variables

# API Configuration
VITE_API_URL=http://localhost:3001/api/v1
VITE_APP_NAME=RepoSensei
VITE_APP_VERSION=1.0.0

# Environment
VITE_NODE_ENV=development

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_PWA=true
VITE_ENABLE_OFFLINE_MODE=true

# External Services (Optional)
VITE_SENTRY_DSN=
VITE_GOOGLE_ANALYTICS_ID=

# Development Settings
VITE_DEBUG_MODE=true
VITE_MOCK_API=false

# Social Media Links
VITE_GITHUB_URL=https://github.com/HectorTa1989/reposensei
VITE_TWITTER_URL=
VITE_DISCORD_URL=

# Contact Information
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_FEEDBACK_URL=https://github.com/HectorTa1989/reposensei/issues
