# 🥷 RepoSensei

> **Your Personal GitHub Code Sensei** - Discover your inner coding ninja through AI-powered GitHub profile analysis

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-20232A?logo=react&logoColor=61DAFB)](https://reactjs.org/)
[![Node.js](https://img.shields.io/badge/Node.js-43853D?logo=node.js&logoColor=white)](https://nodejs.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-316192?logo=postgresql&logoColor=white)](https://www.postgresql.org/)

RepoSensei analyzes your GitHub profile and repositories using advanced AI to assign you a unique anime character from Naruto or Demon Slayer that matches your coding style, impact, and developer persona. Get personalized insights, beautiful character avatars, and discover what your code says about you!

## 🌟 Features

- **🤖 AI-Powered Analysis**: Advanced LLM analysis of your GitHub profile and repositories
- **🎨 Character Assignment**: Get matched with anime characters from Naruto & Demon Slayer
- **🖼️ Custom Avatars**: AI-generated character avatars based on your coding style
- **📊 Detailed Insights**: Repository breakdowns, language statistics, and contribution analysis
- **💾 Save & Share**: Save favorite analyses and share results on social media
- **🔐 Secure Authentication**: User accounts with secure JWT-based authentication
- **📱 Progressive Web App**: Works offline with PWA capabilities
- **🌙 Dark/Light Mode**: Beautiful UI with theme switching
- **⚡ Real-time Updates**: Live analysis progress with WebSocket updates

## 🎯 Product Name Options

Based on market research and domain availability:

| Name | Domain | Status | Description |
|------|--------|--------|-------------|
| **RepoSensei** | `reposensei.com` | ✅ Available | Perfect evolution of Git-Sensei, emphasizes repository wisdom |
| **CodeKata** | `codekata.com` | ✅ Available | Programming practice/discipline, martial arts training reference |
| **GitMaster** | `gitmaster.com` | 🔍 Check | Simple, authoritative, easy to remember |
| **DevSage** | `devsage.com` | 🔍 Check | Wise developer, knowledge-focused |
| **CodeZen** | `codezen.com` | 🔍 Check | Peaceful mastery, mindful coding |

**🏆 Recommended**: **RepoSensei** - Maintains brand continuity, available domain, professional and memorable.

## 🏗️ System Architecture

```mermaid
graph TB
    %% User Interface Layer
    subgraph "Frontend Layer"
        UI[React Frontend<br/>Vite + TypeScript]
        PWA[Progressive Web App<br/>Service Worker]
        UI --> PWA
    end

    %% API Gateway & Load Balancer
    subgraph "API Gateway"
        LB[Load Balancer<br/>Cloudflare/Nginx]
        CORS[CORS Handler]
        RATE[Rate Limiter]
        LB --> CORS
        CORS --> RATE
    end

    %% Backend Services
    subgraph "Backend Services"
        API[Express.js API Server<br/>Node.js/TypeScript]
        AUTH[Authentication Service<br/>JWT + Supabase Auth]
        CACHE[Redis Cache<br/>Session & API Cache]
        QUEUE[Job Queue<br/>Bull/Redis]

        API --> AUTH
        API --> CACHE
        API --> QUEUE
    end

    %% AI/ML Services
    subgraph "AI Processing Layer"
        LLM[Local LLM Service<br/>Llama 3.1 via Ollama]
        IMG[Image Generation<br/>Stable Diffusion XL]
        EMBED[Text Embeddings<br/>Sentence Transformers]

        LLM --> EMBED
    end

    %% External APIs
    subgraph "External APIs"
        GITHUB[GitHub API<br/>User & Repo Data]
        HF[Hugging Face API<br/>Backup LLM Service]
        UNSPLASH[Unsplash API<br/>Fallback Images]
    end

    %% Database Layer
    subgraph "Database Layer"
        MAIN[(PostgreSQL<br/>Supabase)]
        VECTOR[(Vector Database<br/>Pinecone/Weaviate)]
        FILES[(File Storage<br/>Supabase Storage)]

        MAIN --> VECTOR
        MAIN --> FILES
    end

    %% User Flow Connections
    UI --> LB
    LB --> API

    %% Backend to External Services
    API --> GITHUB
    API --> LLM
    API --> IMG
    LLM -.-> HF
    IMG -.-> UNSPLASH

    %% Database Connections
    API --> MAIN
    API --> VECTOR
    API --> FILES

    %% Styling
    classDef frontend fill:#e1f5fe
    classDef backend fill:#f3e5f5
    classDef database fill:#e8f5e8
    classDef external fill:#fff3e0
    classDef ai fill:#fce4ec

    class UI,PWA frontend
    class API,AUTH,CACHE,QUEUE backend
    class MAIN,VECTOR,FILES database
    class GITHUB,HF,UNSPLASH external
    class LLM,IMG,EMBED ai
```

## 🔄 User Workflow

```mermaid
flowchart TD
    START([User Visits RepoSensei]) --> LANDING{Landing Page}

    LANDING --> |New User| SIGNUP[Sign Up / Register]
    LANDING --> |Existing User| LOGIN[Login]
    LANDING --> |Guest Mode| GUEST[Continue as Guest]

    SIGNUP --> AUTH_PROCESS[Authentication Process]
    LOGIN --> AUTH_PROCESS
    AUTH_PROCESS --> |Success| DASHBOARD[User Dashboard]
    AUTH_PROCESS --> |Failed| AUTH_ERROR[Show Error Message]
    AUTH_ERROR --> LANDING

    GUEST --> SEARCH_FORM[GitHub Username Search]
    DASHBOARD --> SEARCH_FORM

    SEARCH_FORM --> |Enter Username| VALIDATE{Validate Input}
    VALIDATE --> |Invalid| ERROR_MSG[Show Error Message]
    ERROR_MSG --> SEARCH_FORM

    VALIDATE --> |Valid| LOADING[Show Loading Animation]
    LOADING --> FETCH_GITHUB[Fetch GitHub Data]

    FETCH_GITHUB --> |Success| GITHUB_DATA[Process User & Repos]
    FETCH_GITHUB --> |API Error| GITHUB_ERROR[GitHub API Error]
    GITHUB_ERROR --> ERROR_DISPLAY[Display Error Message]
    ERROR_DISPLAY --> SEARCH_FORM

    GITHUB_DATA --> AI_ANALYSIS[AI Character Analysis]
    AI_ANALYSIS --> CHARACTER_RESULT[Character Assignment Result]

    CHARACTER_RESULT --> IMG_GEN[Generate Character Avatar]
    IMG_GEN --> |Success| COMPLETE_RESULT[Complete Analysis Result]
    IMG_GEN --> |Failed| FALLBACK_IMG[Use Fallback Image]
    FALLBACK_IMG --> COMPLETE_RESULT

    COMPLETE_RESULT --> DISPLAY_RESULTS[Display Results Page]
    DISPLAY_RESULTS --> USER_ACTIONS{User Actions}

    USER_ACTIONS --> |Share| SHARE_OPTIONS[Share Options]
    USER_ACTIONS --> |Save| SAVE_RESULT[Save to Favorites]
    USER_ACTIONS --> |Download| DOWNLOAD[Download Avatar]
    USER_ACTIONS --> |New Search| SEARCH_FORM
    USER_ACTIONS --> |View Details| DETAILED_VIEW[Detailed Analysis View]

    classDef startEnd fill:#4CAF50,stroke:#2E7D32,color:#fff
    classDef process fill:#2196F3,stroke:#1565C0,color:#fff
    classDef decision fill:#FF9800,stroke:#E65100,color:#fff
    classDef error fill:#F44336,stroke:#C62828,color:#fff
    classDef success fill:#8BC34A,stroke:#558B2F,color:#fff

    class START,LANDING startEnd
    class SEARCH_FORM,FETCH_GITHUB,AI_ANALYSIS,IMG_GEN,DISPLAY_RESULTS process
    class VALIDATE,USER_ACTIONS decision
    class ERROR_MSG,GITHUB_ERROR,ERROR_DISPLAY error
    class COMPLETE_RESULT success
```

## 💰 API Cost Analysis & Free Alternatives

### **Current Gemini API Costs**
- **Commercial Use**: ❌ **Payment Required**
- **Gemini 2.5 Flash**: $0.30 input / $2.50 output per million tokens
- **Imagen 3**: $0.03 per image
- **Monthly Cost Estimate**: $50-500+ for moderate usage

### **🆓 Free Open-Source Alternatives**

| Component | Current (Paid) | Free Alternative | Hosting |
|-----------|----------------|------------------|---------|
| **Text Analysis** | Gemini API | Llama 3.1 8B | Hugging Face / Local |
| **Image Generation** | Imagen 3 | Stable Diffusion XL | Hugging Face / Local |
| **Embeddings** | Gemini Embeddings | Sentence Transformers | Local |
| **Hosting** | Google Cloud | Railway/Render/Vercel | Free Tier |

**💡 Recommended Stack**:
- **LLM**: Llama 3.1 8B via Ollama (local) or Hugging Face Inference API
- **Image**: Stable Diffusion XL via Hugging Face or local deployment
- **Database**: Supabase (free tier)
- **Hosting**: Vercel (frontend) + Railway (backend)

## 📁 Project Structure

```
reposensei/
├── 📁 frontend/                          # React Frontend Application
│   ├── 📁 src/
│   │   ├── 📁 components/                # React Components
│   │   │   ├── 📁 common/                # Reusable UI components
│   │   │   ├── 📁 layout/                # Layout components
│   │   │   ├── 📁 features/              # Feature-specific components
│   │   │   │   ├── 📁 auth/              # Authentication components
│   │   │   │   ├── 📁 search/            # Search functionality
│   │   │   │   ├── 📁 profile/           # Profile display components
│   │   │   │   └── 📁 dashboard/         # Dashboard components
│   │   ├── 📁 hooks/                     # Custom React hooks
│   │   ├── 📁 services/                  # API service layer
│   │   ├── 📁 store/                     # State management (Redux)
│   │   ├── 📁 utils/                     # Utility functions
│   │   ├── 📁 types/                     # TypeScript type definitions
│   │   └── 📁 styles/                    # CSS and styling
│   ├── package.json
│   ├── tsconfig.json
│   └── vite.config.ts
│
├── 📁 backend/                           # Node.js Backend API
│   ├── 📁 src/
│   │   ├── 📁 controllers/               # Route controllers
│   │   ├── 📁 middleware/                # Express middleware
│   │   ├── 📁 routes/                    # API route definitions
│   │   ├── 📁 services/                  # Business logic services
│   │   │   └── 📁 ai/                    # AI service implementations
│   │   ├── 📁 models/                    # Database models
│   │   ├── 📁 database/                  # Database configuration
│   │   ├── 📁 utils/                     # Backend utilities
│   │   └── 📁 config/                    # Configuration files
│   ├── package.json
│   └── tsconfig.json
│
├── 📁 ai-services/                       # AI/ML Microservices
│   ├── 📁 llm-service/                   # Local LLM service (Python)
│   ├── 📁 image-service/                 # Image generation service
│   └── 📁 embedding-service/             # Text embedding service
│
├── 📁 infrastructure/                    # Infrastructure as Code
│   ├── 📁 docker/                        # Docker configurations
│   ├── 📁 kubernetes/                    # K8s manifests
│   └── 📁 terraform/                     # Terraform scripts
│
├── 📁 .github/workflows/                 # GitHub Actions CI/CD
├── 📁 docs/                              # Documentation
├── 📄 README.md
├── 📄 docker-compose.yml
└── 📄 package.json
```

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+ and npm/yarn
- **PostgreSQL** 14+ (or use Supabase)
- **Redis** 6+ (for caching and queues)
- **Python** 3.9+ (for AI services)
- **Docker** (optional, for containerized deployment)

### 🔧 Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/HectorTa1989/reposensei.git
   cd reposensei
   ```

2. **Install dependencies**
   ```bash
   # Install root dependencies
   npm install

   # Install frontend dependencies
   cd frontend && npm install && cd ..

   # Install backend dependencies
   cd backend && npm install && cd ..
   ```

3. **Environment Configuration**
   ```bash
   # Copy environment templates
   cp .env.example .env
   cp frontend/.env.example frontend/.env.local
   cp backend/.env.example backend/.env
   ```

4. **Configure Environment Variables**
   ```bash
   # .env (root)
   NODE_ENV=development

   # frontend/.env.local
   VITE_API_URL=http://localhost:3001
   VITE_APP_NAME=RepoSensei

   # backend/.env
   PORT=3001
   DATABASE_URL=postgresql://user:password@localhost:5432/reposensei
   REDIS_URL=redis://localhost:6379
   JWT_SECRET=your-super-secret-jwt-key
   GITHUB_TOKEN=your-github-personal-access-token

   # AI Service Configuration (Free Alternatives)
   HUGGINGFACE_API_KEY=your-hf-api-key  # Optional, for backup
   OLLAMA_HOST=http://localhost:11434   # For local LLM
   ```

5. **Database Setup**
   ```bash
   # Using Supabase (Recommended)
   # 1. Create account at https://supabase.com
   # 2. Create new project
   # 3. Copy connection string to DATABASE_URL

   # OR Local PostgreSQL
   createdb reposensei
   cd backend && npm run migrate
   ```

6. **Start Development Servers**
   ```bash
   # Terminal 1: Start backend
   cd backend && npm run dev

   # Terminal 2: Start frontend
   cd frontend && npm run dev

   # Terminal 3: Start AI services (optional)
   cd ai-services && docker-compose up
   ```

7. **Access the Application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3001
   - API Documentation: http://localhost:3001/docs

## 🔑 Authentication Setup

### Supabase Authentication (Recommended)

1. **Create Supabase Project**
   ```bash
   # Visit https://supabase.com and create new project
   # Copy your project URL and anon key
   ```

2. **Configure Authentication**
   ```javascript
   // frontend/src/config/supabase.ts
   export const supabaseUrl = 'https://your-project.supabase.co'
   export const supabaseAnonKey = 'your-anon-key'
   ```

3. **Enable Authentication Providers**
   - GitHub OAuth (recommended for GitHub integration)
   - Email/Password
   - Google OAuth (optional)

### Manual JWT Setup

```javascript
// backend/src/config/auth.ts
export const authConfig = {
  jwtSecret: process.env.JWT_SECRET,
  jwtExpiration: '7d',
  bcryptRounds: 12,
  rateLimiting: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5 // limit each IP to 5 requests per windowMs
  }
}
```

## 🤖 AI Services Configuration

### Option 1: Local AI Services (Recommended for Development)

```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull Llama 3.1 model
ollama pull llama3.1:8b

# Start Ollama service
ollama serve
```

### Option 2: Hugging Face Integration

```javascript
// backend/src/services/ai/llmService.ts
import { HfInference } from '@huggingface/inference'

const hf = new HfInference(process.env.HUGGINGFACE_API_KEY)

export const analyzeProfile = async (profileData) => {
  const response = await hf.textGeneration({
    model: 'meta-llama/Llama-2-7b-chat-hf',
    inputs: generatePrompt(profileData),
    parameters: {
      max_new_tokens: 500,
      temperature: 0.7,
    }
  })
  return response.generated_text
}
```

### Option 3: Docker AI Services

```bash
# Start all AI services
cd ai-services
docker-compose up -d

# Services will be available at:
# - LLM Service: http://localhost:8001
# - Image Service: http://localhost:8002
# - Embedding Service: http://localhost:8003
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Frontend tests
cd frontend && npm test

# Backend tests
cd backend && npm test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:coverage
```

### Test Structure

```
tests/
├── 📁 unit/                    # Unit tests
│   ├── components/
│   ├── services/
│   └── utils/
├── 📁 integration/             # Integration tests
│   ├── api/
│   └── database/
└── 📁 e2e/                     # End-to-end tests
    ├── auth.spec.ts
    ├── search.spec.ts
    └── profile.spec.ts
```

## 🚢 Deployment

### Production Environment Variables

```bash
# Production .env
NODE_ENV=production
DATABASE_URL=***********************************/reposensei
REDIS_URL=redis://prod-redis:6379
JWT_SECRET=super-secure-production-secret
GITHUB_TOKEN=github_pat_production_token
FRONTEND_URL=https://reposensei.com
BACKEND_URL=https://api.reposensei.com
```

### Deployment Options

#### Option 1: Vercel + Railway (Recommended)

```bash
# Deploy frontend to Vercel
cd frontend
npx vercel --prod

# Deploy backend to Railway
cd backend
# Connect to Railway and deploy via Git
```

#### Option 2: Docker Deployment

```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy to production
docker-compose -f docker-compose.prod.yml up -d
```

#### Option 3: Kubernetes

```bash
# Apply Kubernetes manifests
kubectl apply -f infrastructure/kubernetes/manifests/

# Check deployment status
kubectl get pods -n reposensei
```

### CI/CD Pipeline

The project includes GitHub Actions workflows for:

- **Continuous Integration**: Run tests on every PR
- **Security Scanning**: Dependency and code security checks
- **Automated Deployment**: Deploy to staging/production
- **Database Migrations**: Automated schema updates

```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm test
      - run: npm run build
```

## 📊 Monitoring & Analytics

### Application Monitoring

```javascript
// backend/src/utils/monitoring.ts
import { createPrometheusMetrics } from './prometheus'

export const metrics = {
  httpRequests: createCounter('http_requests_total'),
  aiAnalysisTime: createHistogram('ai_analysis_duration_seconds'),
  githubApiCalls: createCounter('github_api_calls_total'),
  userRegistrations: createCounter('user_registrations_total')
}
```

### Health Checks

```javascript
// backend/src/routes/health.ts
app.get('/health', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      database: await checkDatabase(),
      redis: await checkRedis(),
      aiService: await checkAIService(),
      githubApi: await checkGitHubAPI()
    }
  }
  res.json(health)
})
```

## 🔒 Security

### Security Features

- **JWT Authentication** with secure token handling
- **Rate Limiting** to prevent API abuse
- **Input Validation** and sanitization
- **CORS Configuration** for cross-origin requests
- **Helmet.js** for security headers
- **SQL Injection Protection** via parameterized queries
- **XSS Protection** with content security policy
- **HTTPS Enforcement** in production

### Security Configuration

```javascript
// backend/src/middleware/security.ts
import helmet from 'helmet'
import rateLimit from 'express-rate-limit'

export const securityMiddleware = [
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"]
      }
    }
  }),
  rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
  })
]
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** and add tests
4. **Run tests**: `npm test`
5. **Commit changes**: `git commit -m 'feat: add amazing feature'`
6. **Push to branch**: `git push origin feature/amazing-feature`
7. **Open a Pull Request**

### Commit Convention

We use [Conventional Commits](https://www.conventionalcommits.org/):

```
feat(scope): add new feature
fix(scope): fix bug
docs(scope): update documentation
style(scope): formatting changes
refactor(scope): code refactoring
test(scope): add tests
chore(scope): maintenance tasks
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Anime Inspiration**: Naruto and Demon Slayer series
- **AI Models**: Llama 3.1, Stable Diffusion XL
- **Open Source Community**: All the amazing libraries and tools
- **GitHub API**: For providing comprehensive developer data

## 📞 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/HectorTa1989/reposensei/issues)
- **Discussions**: [GitHub Discussions](https://github.com/HectorTa1989/reposensei/discussions)
- **Email**: <EMAIL>

---

<div align="center">

**Made with ❤️ by developers, for developers**

[Website](https://reposensei.com) • [Documentation](docs/) • [API](https://api.reposensei.com/docs) • [Community](https://github.com/HectorTa1989/reposensei/discussions)

</div>
