import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';
import { logger, logError, logSecurityEvent } from '@/utils/logger';

// Custom error classes
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;

  constructor(message: string, statusCode: number, isOperational = true, code?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, true, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401, true, 'AUTHENTICATION_ERROR');
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, true, 'AUTHORIZATION_ERROR');
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, true, 'NOT_FOUND_ERROR');
    this.name = 'NotFoundError';
  }
}

export class RateLimitError extends AppError {
  public retryAfter: number;

  constructor(message: string = 'Rate limit exceeded', retryAfter: number = 3600) {
    super(message, 429, true, 'RATE_LIMIT_ERROR');
    this.name = 'RateLimitError';
    this.retryAfter = retryAfter;
  }
}

export class ExternalServiceError extends AppError {
  public service: string;

  constructor(message: string, service: string, statusCode: number = 503) {
    super(message, statusCode, true, 'EXTERNAL_SERVICE_ERROR');
    this.name = 'ExternalServiceError';
    this.service = service;
  }
}

// Error handler middleware
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const requestId = req.id || 'unknown';
  const duration = req.startTime ? Date.now() - req.startTime : 0;

  // Log error details
  logError('Request error', error, {
    requestId,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    duration,
    body: req.body,
    query: req.query,
    params: req.params
  });

  // Handle different error types
  if (error instanceof ZodError) {
    handleValidationError(error, req, res);
    return;
  }

  if (error instanceof AppError) {
    handleAppError(error, req, res);
    return;
  }

  // Handle specific error types
  if (error.name === 'JsonWebTokenError') {
    handleJWTError(error, req, res);
    return;
  }

  if (error.name === 'TokenExpiredError') {
    handleTokenExpiredError(error, req, res);
    return;
  }

  if (error.name === 'MulterError') {
    handleMulterError(error, req, res);
    return;
  }

  // Handle database errors
  if (error.message.includes('duplicate key') || error.code === '23505') {
    handleDuplicateKeyError(error, req, res);
    return;
  }

  if (error.message.includes('foreign key') || error.code === '23503') {
    handleForeignKeyError(error, req, res);
    return;
  }

  // Handle network/timeout errors
  if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
    handleNetworkError(error, req, res);
    return;
  }

  // Handle syntax errors
  if (error instanceof SyntaxError && 'body' in error) {
    handleSyntaxError(error, req, res);
    return;
  }

  // Default error handler
  handleGenericError(error, req, res);
};

// Specific error handlers
const handleValidationError = (error: ZodError, req: Request, res: Response): void => {
  const formattedErrors = error.errors.map(err => ({
    field: err.path.join('.'),
    message: err.message,
    code: err.code,
    received: err.received
  }));

  res.status(400).json({
    success: false,
    error: 'Validation failed',
    code: 'VALIDATION_ERROR',
    details: formattedErrors,
    requestId: req.id
  });
};

const handleAppError = (error: AppError, req: Request, res: Response): void => {
  const response: any = {
    success: false,
    error: error.message,
    code: error.code,
    requestId: req.id
  };

  // Add retry information for rate limit errors
  if (error instanceof RateLimitError) {
    response.retryAfter = error.retryAfter;
    res.set('Retry-After', error.retryAfter.toString());
  }

  // Add service information for external service errors
  if (error instanceof ExternalServiceError) {
    response.service = error.service;
  }

  // Log security events for auth errors
  if (error instanceof AuthenticationError || error instanceof AuthorizationError) {
    logSecurityEvent('authentication_error', {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.originalUrl
    });
  }

  res.status(error.statusCode).json(response);
};

const handleJWTError = (error: Error, req: Request, res: Response): void => {
  logSecurityEvent('jwt_error', {
    error: error.message,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(401).json({
    success: false,
    error: 'Invalid authentication token',
    code: 'INVALID_TOKEN',
    requestId: req.id
  });
};

const handleTokenExpiredError = (error: Error, req: Request, res: Response): void => {
  logSecurityEvent('token_expired', {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(401).json({
    success: false,
    error: 'Authentication token has expired',
    code: 'TOKEN_EXPIRED',
    requestId: req.id
  });
};

const handleMulterError = (error: any, req: Request, res: Response): void => {
  let message = 'File upload error';
  let statusCode = 400;

  switch (error.code) {
    case 'LIMIT_FILE_SIZE':
      message = 'File too large';
      break;
    case 'LIMIT_FILE_COUNT':
      message = 'Too many files';
      break;
    case 'LIMIT_UNEXPECTED_FILE':
      message = 'Unexpected file field';
      break;
    default:
      message = error.message || 'File upload error';
  }

  res.status(statusCode).json({
    success: false,
    error: message,
    code: 'FILE_UPLOAD_ERROR',
    requestId: req.id
  });
};

const handleDuplicateKeyError = (error: Error, req: Request, res: Response): void => {
  res.status(409).json({
    success: false,
    error: 'Resource already exists',
    code: 'DUPLICATE_RESOURCE',
    requestId: req.id
  });
};

const handleForeignKeyError = (error: Error, req: Request, res: Response): void => {
  res.status(400).json({
    success: false,
    error: 'Invalid reference to related resource',
    code: 'FOREIGN_KEY_ERROR',
    requestId: req.id
  });
};

const handleNetworkError = (error: any, req: Request, res: Response): void => {
  res.status(503).json({
    success: false,
    error: 'External service unavailable',
    code: 'SERVICE_UNAVAILABLE',
    requestId: req.id
  });
};

const handleSyntaxError = (error: SyntaxError, req: Request, res: Response): void => {
  res.status(400).json({
    success: false,
    error: 'Invalid JSON in request body',
    code: 'SYNTAX_ERROR',
    requestId: req.id
  });
};

const handleGenericError = (error: Error, req: Request, res: Response): void => {
  // Don't expose internal errors in production
  const isDevelopment = process.env.NODE_ENV === 'development';

  res.status(500).json({
    success: false,
    error: isDevelopment ? error.message : 'Internal server error',
    code: 'INTERNAL_ERROR',
    requestId: req.id,
    ...(isDevelopment && { stack: error.stack })
  });
};

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Create error instances
export const createError = {
  validation: (message: string, details?: any) => new ValidationError(message, details),
  authentication: (message?: string) => new AuthenticationError(message),
  authorization: (message?: string) => new AuthorizationError(message),
  notFound: (message?: string) => new NotFoundError(message),
  rateLimit: (message?: string, retryAfter?: number) => new RateLimitError(message, retryAfter),
  externalService: (message: string, service: string, statusCode?: number) => 
    new ExternalServiceError(message, service, statusCode),
  generic: (message: string, statusCode: number = 500, code?: string) => 
    new AppError(message, statusCode, true, code)
};

export default errorHandler;
