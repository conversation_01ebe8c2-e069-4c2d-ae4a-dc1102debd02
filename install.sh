#!/bin/bash

echo "🚀 Installing RepoSensei Dependencies..."
echo

echo "📦 Installing Frontend Dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "❌ Frontend installation failed!"
    exit 1
fi

echo
echo "📦 Installing Backend Dependencies..."
cd backend
npm install
if [ $? -ne 0 ]; then
    echo "❌ Backend installation failed!"
    exit 1
fi

cd ..
echo
echo "✅ Installation completed successfully!"
echo
echo "🎯 To start development:"
echo "  1. Backend: cd backend && npm run dev"
echo "  2. Frontend: npm run dev"
echo
echo "🌐 Access the app at: http://localhost:5173"
echo "📡 Backend API at: http://localhost:3001"
echo
