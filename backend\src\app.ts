import express, { Application, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { logger, stream, logError } from '@/utils/logger';
import { validationMiddleware } from '@/middleware/validation';

// Import routes
import analysisRoutes from '@/routes/analysis';
import healthRoutes from '@/routes/health';

// Import error handler
import { errorHandler } from '@/middleware/errorHandler';

class App {
  public app: Application;

  constructor() {
    this.app = express();
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
        },
      },
      crossOriginEmbedderPolicy: false,
    }));

    // CORS configuration
    const corsOptions = {
      origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
        const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
          'http://localhost:3000',
          'http://localhost:5173',
          'https://reposensei.com',
          'https://www.reposensei.com'
        ];

        // Allow requests with no origin (mobile apps, etc.)
        if (!origin) return callback(null, true);

        if (allowedOrigins.includes(origin)) {
          callback(null, true);
        } else {
          callback(new Error('Not allowed by CORS'));
        }
      },
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key'],
      exposedHeaders: ['X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset']
    };

    this.app.use(cors(corsOptions));

    // Compression middleware
    this.app.use(compression({
      filter: (req, res) => {
        if (req.headers['x-no-compression']) {
          return false;
        }
        return compression.filter(req, res);
      },
      threshold: 1024 // Only compress responses larger than 1KB
    }));

    // Body parsing middleware
    this.app.use(express.json({ 
      limit: '10mb',
      verify: (req: any, res, buf) => {
        req.rawBody = buf;
      }
    }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use(morgan('combined', { stream }));

    // Global rate limiting
    const globalRateLimit = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: process.env.NODE_ENV === 'production' ? 1000 : 10000, // Limit each IP
      message: {
        success: false,
        error: 'Too many requests from this IP, please try again later.',
        retryAfter: 900
      },
      standardHeaders: true,
      legacyHeaders: false,
      handler: (req, res) => {
        logger.warn('Global rate limit exceeded', {
          ip: req.ip,
          userAgent: req.get('User-Agent')
        });
        res.status(429).json({
          success: false,
          error: 'Too many requests from this IP, please try again later.',
          retryAfter: 900
        });
      }
    });

    if (process.env.ENABLE_RATE_LIMITING !== 'false') {
      this.app.use(globalRateLimit);
    }

    // Request sanitization
    this.app.use(validationMiddleware.sanitize);

    // Request ID middleware for tracing
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      req.id = Math.random().toString(36).substring(2, 15);
      res.setHeader('X-Request-ID', req.id);
      next();
    });

    // Request timing middleware
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      req.startTime = Date.now();
      next();
    });
  }

  private initializeRoutes(): void {
    // Health check endpoint (before other middleware)
    this.app.use('/health', healthRoutes);

    // API version prefix
    const apiPrefix = `/api/${process.env.API_VERSION || 'v1'}`;

    // Main API routes
    this.app.use(`${apiPrefix}/analysis`, analysisRoutes);

    // Root endpoint
    this.app.get('/', (req: Request, res: Response) => {
      res.json({
        success: true,
        message: 'RepoSensei API Server',
        version: process.env.API_VERSION || 'v1',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development'
      });
    });

    // API documentation endpoint
    this.app.get(`${apiPrefix}/docs`, (req: Request, res: Response) => {
      res.json({
        success: true,
        message: 'API Documentation',
        endpoints: {
          analysis: {
            'POST /analysis/analyze': 'Analyze a GitHub profile',
            'POST /analysis/batch': 'Batch analyze multiple profiles',
            'GET /analysis/history': 'Get analysis history (auth required)',
            'GET /analysis/stats': 'Get global statistics'
          },
          health: {
            'GET /health': 'Health check',
            'GET /health/detailed': 'Detailed health check'
          }
        },
        authentication: {
          type: 'Bearer Token',
          header: 'Authorization: Bearer <token>'
        },
        rateLimit: {
          global: '1000 requests per 15 minutes per IP',
          analysis: '10 requests per 15 minutes per IP',
          batch: '3 requests per hour per IP'
        }
      });
    });

    // 404 handler for undefined routes
    this.app.use('*', (req: Request, res: Response) => {
      logger.warn('Route not found', {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      res.status(404).json({
        success: false,
        error: 'Route not found',
        message: `Cannot ${req.method} ${req.originalUrl}`,
        availableEndpoints: [
          'GET /',
          `GET ${apiPrefix}/docs`,
          `POST ${apiPrefix}/analysis/analyze`,
          `POST ${apiPrefix}/analysis/batch`,
          `GET ${apiPrefix}/analysis/history`,
          `GET ${apiPrefix}/analysis/stats`,
          'GET /health'
        ]
      });
    });
  }

  private initializeErrorHandling(): void {
    // Global error handler
    this.app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
      const requestId = req.id || 'unknown';
      const duration = req.startTime ? Date.now() - req.startTime : 0;

      logError('Unhandled error', error, {
        requestId,
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        duration
      });

      // Don't expose internal errors in production
      const isDevelopment = process.env.NODE_ENV === 'development';

      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: isDevelopment ? error.message : 'Something went wrong',
        requestId,
        ...(isDevelopment && { stack: error.stack })
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error: Error) => {
      logError('Uncaught Exception', error);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
      logError('Unhandled Rejection', new Error(reason), { promise });
      process.exit(1);
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      logger.info('SIGTERM received, shutting down gracefully');
      process.exit(0);
    });

    process.on('SIGINT', () => {
      logger.info('SIGINT received, shutting down gracefully');
      process.exit(0);
    });
  }

  public getApp(): Application {
    return this.app;
  }
}

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      id?: string;
      startTime?: number;
      rawBody?: Buffer;
    }
  }
}

export default App;
