import { Request, Response } from 'express';
import { z } from 'zod';
import { githubService } from '@/services/githubService';
import { llmService } from '@/services/ai/llmService';
import { imageService } from '@/services/ai/imageService';
import { logger, logError, logBusinessEvent } from '@/utils/logger';
import { redis } from '@/config/redis';

// Validation schemas
const analyzeRequestSchema = z.object({
  username: z.string()
    .min(1, 'Username is required')
    .max(39, 'Username too long')
    .regex(/^[a-zA-Z0-9]([a-zA-Z0-9-])*[a-zA-Z0-9]$/, 'Invalid GitHub username format')
});

const batchAnalyzeSchema = z.object({
  usernames: z.array(z.string()).min(1).max(10, 'Maximum 10 usernames allowed')
});

interface AnalysisResult {
  username: string;
  character: {
    name: string;
    anime: string;
    reason: string;
    color: string;
  };
  avatar: string;
  profile: {
    name: string | null;
    bio: string | null;
    followers: number;
    publicRepos: number;
    totalStars: number;
    primaryLanguages: string[];
  };
  timestamp: string;
}

class AnalysisController {
  /**
   * Analyze a single GitHub profile
   */
  public async analyzeProfile(req: Request, res: Response): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Validate request
      const { username } = analyzeRequestSchema.parse(req.body);
      const userId = req.user?.id; // From auth middleware

      logger.info('Starting profile analysis', { username, userId });

      // Check if analysis is already in progress
      const lockKey = `analysis_lock:${username}`;
      const isLocked = await redis.exists(lockKey);
      
      if (isLocked) {
        res.status(429).json({
          success: false,
          error: 'Analysis already in progress for this user',
          retryAfter: 30
        });
        return;
      }

      // Set lock for 5 minutes
      await redis.set(lockKey, 'locked', 300);

      try {
        // Step 1: Fetch GitHub profile
        const githubProfile = await githubService.getUserProfile(username);
        
        // Step 2: Generate character analysis
        const characterAnalysis = await llmService.analyzeProfile(githubProfile);
        
        // Step 3: Generate avatar image
        const avatarUrl = await imageService.generateAvatar(characterAnalysis);

        // Step 4: Prepare response
        const result: AnalysisResult = {
          username: githubProfile.user.login,
          character: {
            name: characterAnalysis.characterName,
            anime: characterAnalysis.anime,
            reason: characterAnalysis.reason,
            color: characterAnalysis.badgeColor
          },
          avatar: avatarUrl,
          profile: {
            name: githubProfile.user.name,
            bio: githubProfile.user.bio,
            followers: githubProfile.user.followers,
            publicRepos: githubProfile.user.public_repos,
            totalStars: githubProfile.stats.totalStars,
            primaryLanguages: Object.keys(githubProfile.stats.primaryLanguages)
              .sort((a, b) => githubProfile.stats.primaryLanguages[b] - githubProfile.stats.primaryLanguages[a])
              .slice(0, 5)
          },
          timestamp: new Date().toISOString()
        };

        // Log business event
        logBusinessEvent('profile_analyzed', userId, {
          username,
          character: characterAnalysis.characterName,
          anime: characterAnalysis.anime,
          duration: Date.now() - startTime
        });

        // Store analysis result for history (if user is authenticated)
        if (userId) {
          await this.saveAnalysisToHistory(userId, result);
        }

        res.json({
          success: true,
          data: result,
          processingTime: Date.now() - startTime
        });

      } finally {
        // Release lock
        await redis.del(lockKey);
      }

    } catch (error) {
      logError('Profile analysis failed', error as Error, { 
        username: req.body.username,
        userId: req.user?.id,
        duration: Date.now() - startTime
      });

      // Handle specific error types
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Invalid request data',
          details: error.errors
        });
        return;
      }

      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          res.status(404).json({
            success: false,
            error: 'GitHub user not found'
          });
          return;
        }

        if (error.message.includes('rate limit')) {
          res.status(429).json({
            success: false,
            error: 'Rate limit exceeded. Please try again later.',
            retryAfter: 3600
          });
          return;
        }
      }

      res.status(500).json({
        success: false,
        error: 'Analysis failed. Please try again later.'
      });
    }
  }

  /**
   * Batch analyze multiple profiles
   */
  public async batchAnalyze(req: Request, res: Response): Promise<void> {
    try {
      const { usernames } = batchAnalyzeSchema.parse(req.body);
      const userId = req.user?.id;

      logger.info('Starting batch analysis', { usernames, userId });

      const results = await Promise.allSettled(
        usernames.map(async (username) => {
          try {
            const githubProfile = await githubService.getUserProfile(username);
            const characterAnalysis = await llmService.analyzeProfile(githubProfile);
            
            return {
              username,
              success: true,
              character: {
                name: characterAnalysis.characterName,
                anime: characterAnalysis.anime,
                reason: characterAnalysis.reason,
                color: characterAnalysis.badgeColor
              },
              profile: {
                name: githubProfile.user.name,
                followers: githubProfile.user.followers,
                totalStars: githubProfile.stats.totalStars
              }
            };
          } catch (error) {
            return {
              username,
              success: false,
              error: error instanceof Error ? error.message : 'Analysis failed'
            };
          }
        })
      );

      const processedResults = results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          return {
            username: usernames[index],
            success: false,
            error: 'Processing failed'
          };
        }
      });

      res.json({
        success: true,
        data: processedResults,
        summary: {
          total: usernames.length,
          successful: processedResults.filter(r => r.success).length,
          failed: processedResults.filter(r => !r.success).length
        }
      });

    } catch (error) {
      logError('Batch analysis failed', error as Error, { userId: req.user?.id });

      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          error: 'Invalid request data',
          details: error.errors
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: 'Batch analysis failed'
      });
    }
  }

  /**
   * Get analysis history for authenticated user
   */
  public async getAnalysisHistory(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);
      const offset = (page - 1) * limit;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      // Get history from cache/database
      const historyKey = `history:${userId}`;
      const history = await redis.getJSON<AnalysisResult[]>(historyKey) || [];

      const paginatedHistory = history
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(offset, offset + limit);

      res.json({
        success: true,
        data: paginatedHistory,
        pagination: {
          page,
          limit,
          total: history.length,
          pages: Math.ceil(history.length / limit)
        }
      });

    } catch (error) {
      logError('Failed to get analysis history', error as Error, { userId: req.user?.id });
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve history'
      });
    }
  }

  /**
   * Get analysis statistics
   */
  public async getAnalysisStats(req: Request, res: Response): Promise<void> {
    try {
      // Get global statistics from cache
      const stats = await redis.getJSON('global_stats') || {
        totalAnalyses: 0,
        popularCharacters: {},
        popularAnimes: { 'Naruto': 0, 'Demon Slayer': 0 },
        topLanguages: {}
      };

      res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      logError('Failed to get analysis stats', error as Error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve statistics'
      });
    }
  }

  /**
   * Save analysis to user history
   */
  private async saveAnalysisToHistory(userId: string, analysis: AnalysisResult): Promise<void> {
    try {
      const historyKey = `history:${userId}`;
      const history = await redis.getJSON<AnalysisResult[]>(historyKey) || [];
      
      // Add new analysis to beginning
      history.unshift(analysis);
      
      // Keep only last 100 analyses
      const trimmedHistory = history.slice(0, 100);
      
      // Save back to cache (expire in 30 days)
      await redis.setJSON(historyKey, trimmedHistory, 2592000);
      
      // Update global statistics
      await this.updateGlobalStats(analysis);
      
    } catch (error) {
      logError('Failed to save analysis to history', error as Error, { userId });
    }
  }

  /**
   * Update global statistics
   */
  private async updateGlobalStats(analysis: AnalysisResult): Promise<void> {
    try {
      const statsKey = 'global_stats';
      const stats = await redis.getJSON(statsKey) || {
        totalAnalyses: 0,
        popularCharacters: {},
        popularAnimes: { 'Naruto': 0, 'Demon Slayer': 0 },
        topLanguages: {}
      };

      // Update counters
      stats.totalAnalyses += 1;
      stats.popularCharacters[analysis.character.name] = 
        (stats.popularCharacters[analysis.character.name] || 0) + 1;
      stats.popularAnimes[analysis.character.anime] += 1;

      // Update language stats
      analysis.profile.primaryLanguages.forEach(lang => {
        stats.topLanguages[lang] = (stats.topLanguages[lang] || 0) + 1;
      });

      // Save updated stats (expire in 1 hour)
      await redis.setJSON(statsKey, stats, 3600);

    } catch (error) {
      logError('Failed to update global stats', error as Error);
    }
  }
}

export const analysisController = new AnalysisController();
export default analysisController;
