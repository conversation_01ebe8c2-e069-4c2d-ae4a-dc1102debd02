# Server Configuration
NODE_ENV=development
PORT=3001
HOST=localhost

# Database Configuration (Supabase)
DATABASE_URL=postgresql://postgres:password@localhost:5432/reposensei
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your-refresh-token-secret
REFRESH_TOKEN_EXPIRES_IN=30d

# GitHub API Configuration
GITHUB_TOKEN=github_pat_your_personal_access_token
GITHUB_CLIENT_ID=your-github-oauth-client-id
GITHUB_CLIENT_SECRET=your-github-oauth-client-secret

# AI Services Configuration
# Hugging Face (Free Alternative)
HUGGINGFACE_API_KEY=hf_your_api_key_here
HUGGINGFACE_MODEL_TEXT=meta-llama/Llama-2-7b-chat-hf
HUGGINGFACE_MODEL_IMAGE=stabilityai/stable-diffusion-xl-base-1.0

# Ollama Configuration (Local LLM)
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=llama3.1:8b

# Image Generation Configuration
STABLE_DIFFUSION_API_URL=http://localhost:7860
UNSPLASH_ACCESS_KEY=your-unsplash-access-key

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
FRONTEND_URL=http://localhost:5173
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Monitoring & Analytics
ENABLE_METRICS=true
PROMETHEUS_PORT=9090

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret
COOKIE_SECURE=false
COOKIE_SAME_SITE=lax

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_SOCIAL_LOGIN=true
ENABLE_EMAIL_VERIFICATION=false
ENABLE_RATE_LIMITING=true
ENABLE_CACHING=true

# External Services
WEBHOOK_SECRET=your-webhook-secret
API_VERSION=v1
