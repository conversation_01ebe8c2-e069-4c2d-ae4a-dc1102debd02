
import axios from 'axios';
import { UserProfile, AnimeBadge } from '../types';

// Use backend API for AI analysis instead of direct Gemini calls
const API_BASE = import.meta.env.VITE_API_URL || 'http://localhost:3001/api/v1';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE,
  timeout: 60000, // AI analysis can take longer
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for auth token
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Add response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

export interface AnalysisResult {
  username: string;
  character: {
    name: string;
    anime: string;
    reason: string;
    color: string;
  };
  avatar: string;
  profile: {
    name: string | null;
    bio: string | null;
    followers: number;
    publicRepos: number;
    totalStars: number;
    primaryLanguages: string[];
  };
  timestamp: string;
}

// Legacy function for backward compatibility
export const getAnimeBadge = async (profileData: UserProfile): Promise<AnimeBadge> => {
  try {
    const username = profileData.user.login;
    const result = await analyzeProfile(username);

    return {
      characterName: result.character.name,
      anime: result.character.anime as 'Naruto' | 'Demon Slayer',
      reason: result.character.reason,
      badgeColor: result.character.color
    };
  } catch (error: any) {
    console.error('Error getting anime badge:', error);

    // Fallback response based on profile data
    return getFallbackBadge(profileData);
  }
};

// Legacy function for backward compatibility
export const generateAvatar = async (badge: AnimeBadge): Promise<string> => {
  try {
    // Since the new API returns the avatar as part of the analysis,
    // we'll need to store it or make a separate call
    // For now, return a placeholder or generate a simple avatar
    return generateFallbackAvatar(badge);
  } catch (error: any) {
    console.error('Error generating avatar:', error);
    return generateFallbackAvatar(badge);
  }
};

// New main analysis function
export const analyzeProfile = async (username: string): Promise<AnalysisResult> => {
  try {
    // Validate username
    if (!username || typeof username !== 'string' || username.trim().length === 0) {
      throw new Error('Invalid GitHub username provided');
    }

    const cleanUsername = username.trim().toLowerCase();

    // Call backend analysis endpoint
    const response = await apiClient.post('/analysis/analyze', {
      username: cleanUsername
    });

    if (!response.data.success) {
      throw new Error(response.data.error || 'Analysis failed');
    }

    return response.data.data;

  } catch (error: any) {
    console.error('Error analyzing profile:', error);

    // Handle specific error types
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.error || error.message;

      switch (status) {
        case 404:
          throw new Error(`GitHub user not found: ${username}`);
        case 429:
          const retryAfter = error.response.data?.retryAfter || 900;
          throw new Error(`Rate limit exceeded. Please try again in ${Math.ceil(retryAfter / 60)} minutes.`);
        case 400:
          throw new Error(message || 'Invalid request data');
        case 500:
          throw new Error('Analysis service temporarily unavailable. Please try again later.');
        default:
          throw new Error(message || 'Analysis failed');
      }
    }

    // Network or timeout errors
    if (error.code === 'ECONNABORTED') {
      throw new Error('Analysis timeout. Please try again with a shorter username or try later.');
    }

    throw new Error(error.message || 'Analysis failed. Please try again.');
  }
};

// Batch analysis function
export const batchAnalyze = async (usernames: string[]): Promise<any[]> => {
  try {
    if (!Array.isArray(usernames) || usernames.length === 0) {
      throw new Error('Invalid usernames array provided');
    }

    if (usernames.length > 10) {
      throw new Error('Maximum 10 usernames allowed for batch analysis');
    }

    const cleanUsernames = usernames.map(u => u.trim().toLowerCase()).filter(u => u.length > 0);

    const response = await apiClient.post('/analysis/batch', {
      usernames: cleanUsernames
    });

    if (!response.data.success) {
      throw new Error(response.data.error || 'Batch analysis failed');
    }

    return response.data.data;

  } catch (error: any) {
    console.error('Error in batch analysis:', error);
    throw new Error(error.response?.data?.error || error.message || 'Batch analysis failed');
  }
};

// Get analysis history
export const getAnalysisHistory = async (page = 1, limit = 10): Promise<{ data: AnalysisResult[]; pagination: any }> => {
  try {
    const response = await apiClient.get('/analysis/history', {
      params: { page, limit }
    });

    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to fetch history');
    }

    return {
      data: response.data.data,
      pagination: response.data.pagination
    };

  } catch (error: any) {
    console.error('Error fetching analysis history:', error);
    throw new Error(error.response?.data?.error || error.message || 'Failed to fetch history');
  }
};

// Get global statistics
export const getAnalysisStats = async (): Promise<any> => {
  try {
    const response = await apiClient.get('/analysis/stats');

    if (!response.data.success) {
      throw new Error(response.data.error || 'Failed to fetch stats');
    }

    return response.data.data;

  } catch (error: any) {
    console.error('Error fetching analysis stats:', error);
    return {
      totalAnalyses: 0,
      popularCharacters: {},
      popularAnimes: { 'Naruto': 0, 'Demon Slayer': 0 },
      topLanguages: {}
    };
  }
};

// Fallback functions
const getFallbackBadge = (profileData: UserProfile): AnimeBadge => {
  const languages = profileData.repos
    .map(repo => repo.language)
    .filter(lang => lang !== null);

  const totalStars = profileData.repos.reduce((sum, repo) => sum + repo.stargazers_count, 0);

  // Simple rule-based fallback
  if (languages.includes('JavaScript') || languages.includes('TypeScript')) {
    return {
      characterName: 'Naruto Uzumaki',
      anime: 'Naruto',
      reason: 'Like Naruto, you bring energy and versatility to your projects with JavaScript/TypeScript, always finding creative solutions.',
      badgeColor: '#FF7F00'
    };
  }

  if (languages.includes('Python')) {
    return {
      characterName: 'Tanjiro Kamado',
      anime: 'Demon Slayer',
      reason: 'Your Python skills show the same adaptability and problem-solving nature as Tanjiro, tackling challenges with determination.',
      badgeColor: '#107C80'
    };
  }

  if (totalStars > 100) {
    return {
      characterName: 'Kakashi Hatake',
      anime: 'Naruto',
      reason: 'Your high-starred repositories demonstrate the wisdom and technical mastery of the Copy Ninja himself.',
      badgeColor: '#708090'
    };
  }

  // Default fallback
  return {
    characterName: 'Zenitsu Agatsuma',
    anime: 'Demon Slayer',
    reason: 'Like Zenitsu, you may seem humble, but your code shows hidden potential and unique strengths waiting to be unleashed.',
    badgeColor: '#FFD700'
  };
};

const generateFallbackAvatar = (badge: AnimeBadge): string => {
  // Generate a simple SVG avatar with character initial and color
  const initial = badge.characterName.charAt(0).toUpperCase();
  const svg = `
    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
      <circle cx="100" cy="100" r="90" fill="${badge.badgeColor}" stroke="#ffffff" stroke-width="4"/>
      <text x="100" y="120" font-family="Arial, sans-serif" font-size="80" font-weight="bold" text-anchor="middle" fill="#ffffff">${initial}</text>
    </svg>
  `;

  return `data:image/svg+xml;base64,${btoa(svg)}`;
};
