import { createClient, RedisClientType } from 'redis';
import { logger } from '@/utils/logger';

class RedisConfig {
  private client: RedisClientType | null = null;
  private isConnected = false;

  constructor() {
    this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
      
      this.client = createClient({
        url: redisUrl,
        password: process.env.REDIS_PASSWORD,
        socket: {
          connectTimeout: 5000,
          lazyConnect: true,
          reconnectStrategy: (retries) => {
            if (retries > 10) {
              logger.error('Redis: Max reconnection attempts reached');
              return false;
            }
            return Math.min(retries * 100, 3000);
          }
        }
      });

      this.client.on('error', (error) => {
        logger.error('Redis Client Error:', error);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        logger.info('Redis: Connected to server');
        this.isConnected = true;
      });

      this.client.on('ready', () => {
        logger.info('Redis: Client ready');
      });

      this.client.on('end', () => {
        logger.info('Redis: Connection ended');
        this.isConnected = false;
      });

      await this.client.connect();
    } catch (error) {
      logger.error('Redis: Failed to initialize:', error);
      this.isConnected = false;
    }
  }

  public getClient(): RedisClientType | null {
    return this.client;
  }

  public isReady(): boolean {
    return this.isConnected && this.client?.isReady === true;
  }

  // Cache operations with error handling
  public async get(key: string): Promise<string | null> {
    try {
      if (!this.isReady()) return null;
      return await this.client!.get(key);
    } catch (error) {
      logger.error('Redis GET error:', { key, error });
      return null;
    }
  }

  public async set(key: string, value: string, ttl?: number): Promise<boolean> {
    try {
      if (!this.isReady()) return false;
      
      if (ttl) {
        await this.client!.setEx(key, ttl, value);
      } else {
        await this.client!.set(key, value);
      }
      return true;
    } catch (error) {
      logger.error('Redis SET error:', { key, error });
      return false;
    }
  }

  public async del(key: string): Promise<boolean> {
    try {
      if (!this.isReady()) return false;
      await this.client!.del(key);
      return true;
    } catch (error) {
      logger.error('Redis DEL error:', { key, error });
      return false;
    }
  }

  public async exists(key: string): Promise<boolean> {
    try {
      if (!this.isReady()) return false;
      const result = await this.client!.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Redis EXISTS error:', { key, error });
      return false;
    }
  }

  public async incr(key: string): Promise<number | null> {
    try {
      if (!this.isReady()) return null;
      return await this.client!.incr(key);
    } catch (error) {
      logger.error('Redis INCR error:', { key, error });
      return null;
    }
  }

  public async expire(key: string, seconds: number): Promise<boolean> {
    try {
      if (!this.isReady()) return false;
      await this.client!.expire(key, seconds);
      return true;
    } catch (error) {
      logger.error('Redis EXPIRE error:', { key, error });
      return false;
    }
  }

  // JSON operations
  public async setJSON(key: string, value: any, ttl?: number): Promise<boolean> {
    try {
      const jsonString = JSON.stringify(value);
      return await this.set(key, jsonString, ttl);
    } catch (error) {
      logger.error('Redis setJSON error:', { key, error });
      return false;
    }
  }

  public async getJSON<T>(key: string): Promise<T | null> {
    try {
      const value = await this.get(key);
      if (!value) return null;
      return JSON.parse(value) as T;
    } catch (error) {
      logger.error('Redis getJSON error:', { key, error });
      return null;
    }
  }

  // Health check
  public async healthCheck(): Promise<{ status: string; latency?: number; error?: string }> {
    const start = Date.now();
    try {
      if (!this.isReady()) {
        return { status: 'unhealthy', error: 'Redis client not ready' };
      }
      
      await this.client!.ping();
      const latency = Date.now() - start;
      return { status: 'healthy', latency };
    } catch (error) {
      return { 
        status: 'unhealthy', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Graceful shutdown
  public async disconnect(): Promise<void> {
    try {
      if (this.client) {
        await this.client.quit();
        logger.info('Redis: Disconnected gracefully');
      }
    } catch (error) {
      logger.error('Redis: Error during disconnect:', error);
    }
  }
}

// Create singleton instance
const redisConfig = new RedisConfig();

export default redisConfig;
export { redisConfig as redis };
