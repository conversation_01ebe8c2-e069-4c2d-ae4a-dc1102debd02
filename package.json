{"name": "reposensei-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "RepoSensei Frontend - AI-powered GitHub profile analysis with anime character matching", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.2", "@tanstack/react-query": "^5.17.1", "react-router-dom": "^6.20.1", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "react-hook-form": "^7.48.2", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0", "react-hot-toast": "^2.4.1", "react-helmet-async": "^2.0.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/node": "^22.14.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "typescript": "~5.7.2", "vite": "^6.2.0", "vite-plugin-pwa": "^0.17.4", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "@vitest/coverage-v8": "^1.0.4", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "tailwindcss": "^3.3.6", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@tailwindcss/aspect-ratio": "^0.4.2", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "jsdom": "^23.0.1"}, "keywords": ["github", "ai", "anime", "profile-analysis", "react", "typescript", "vite"], "author": "HectorTa1989", "license": "MIT", "engines": {"node": ">=18.0.0"}}