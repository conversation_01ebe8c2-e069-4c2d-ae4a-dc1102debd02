version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: reposensei-postgres
    environment:
      POSTGRES_DB: reposensei
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - reposensei-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: reposensei-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - reposensei-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: reposensei-backend
    environment:
      NODE_ENV: development
      PORT: 3001
      DATABASE_URL: ***********************************************/reposensei
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production
      GITHUB_TOKEN: ${GITHUB_TOKEN}
      HUGGINGFACE_API_KEY: ${HUGGINGFACE_API_KEY}
      OLLAMA_HOST: http://ollama:11434
      STABLE_DIFFUSION_API_URL: http://stable-diffusion:7860
      UNSPLASH_ACCESS_KEY: ${UNSPLASH_ACCESS_KEY}
      FRONTEND_URL: http://localhost:5173
      ALLOWED_ORIGINS: http://localhost:5173,http://localhost:3000
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - reposensei-network
    volumes:
      - ./backend:/app
      - /app/node_modules
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: reposensei-frontend
    environment:
      VITE_API_URL: http://localhost:3001/api/v1
      VITE_APP_NAME: RepoSensei
      VITE_NODE_ENV: development
    ports:
      - "5173:5173"
    depends_on:
      - backend
    networks:
      - reposensei-network
    volumes:
      - .:/app
      - /app/node_modules

  # Ollama (Local LLM)
  ollama:
    image: ollama/ollama:latest
    container_name: reposensei-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - reposensei-network
    environment:
      - OLLAMA_KEEP_ALIVE=24h
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Stable Diffusion (Optional - requires GPU)
  stable-diffusion:
    image: continuumio/miniconda3
    container_name: reposensei-stable-diffusion
    ports:
      - "7860:7860"
    volumes:
      - ./ai-services/stable-diffusion:/app
      - stable_diffusion_models:/app/models
    networks:
      - reposensei-network
    environment:
      - PYTHONPATH=/app
    command: >
      bash -c "
        cd /app &&
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu &&
        pip install diffusers transformers accelerate &&
        python app.py
      "
    profiles:
      - gpu

  # Nginx (Production reverse proxy)
  nginx:
    image: nginx:alpine
    container_name: reposensei-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - reposensei-network
    profiles:
      - production

volumes:
  postgres_data:
  redis_data:
  ollama_data:
  stable_diffusion_models:

networks:
  reposensei-network:
    driver: bridge
