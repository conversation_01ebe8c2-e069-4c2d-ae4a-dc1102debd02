# 🚀 RepoSensei Development Setup

## Quick Start (Minimal Dependencies)

### 1. Install Dependencies

```bash
# Frontend dependencies
npm install

# Backend dependencies
cd backend
npm install
cd ..
```

### 2. Environment Setup

The project is configured to work with minimal external dependencies for development.

**Backend** (`backend/.env`):
- Uses local PostgreSQL (optional - will fallback to in-memory storage)
- Uses local Redis (optional - will fallback to in-memory cache)
- Supabase is optional (will use JWT-only auth)
- AI services are optional (will use fallback responses)

**Frontend** (`.env`):
- Already configured to connect to local backend

### 3. Start Development Servers

```bash
# Terminal 1: Start Backend
cd backend
npm run dev

# Terminal 2: Start Frontend
npm run dev
```

### 4. Access the Application

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3001
- **Health Check**: http://localhost:3001/health

## Optional External Services

### GitHub Token (Recommended)
1. Go to GitHub Settings > Developer settings > Personal access tokens
2. Generate a new token with `public_repo` scope
3. Add to `backend/.env`: `GITHUB_TOKEN=your_token_here`

### Database (Optional)
- **PostgreSQL**: Install locally or use Docker
- **Redis**: Install locally or use Docker
- **Supabase**: Create account at supabase.com for cloud database

### AI Services (Optional)
- **Ollama**: Install for local LLM processing
- **Hugging Face**: Get free API key for AI services
- **Stable Diffusion**: Set up local instance for image generation

## Docker Setup (Alternative)

```bash
# Start all services with Docker
docker-compose up

# Or just databases
docker-compose up postgres redis
```

## Troubleshooting

### Backend Issues
- **Supabase Error**: Ignore if not using Supabase (will use fallback)
- **Redis Error**: Will use in-memory cache as fallback
- **Database Error**: Will use in-memory storage as fallback

### Frontend Issues
- **Vite Not Found**: Run `npm install` to install dependencies locally
- **Module Errors**: Clear node_modules and reinstall: `rm -rf node_modules && npm install`

### Common Solutions
```bash
# Clear all caches and reinstall
rm -rf node_modules package-lock.json
npm install

# Backend
cd backend
rm -rf node_modules package-lock.json
npm install
```
