{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "paths": {"@/*": ["./src/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/services/*": ["./src/services/*"], "@/middleware/*": ["./src/middleware/*"], "@/controllers/*": ["./src/controllers/*"], "@/models/*": ["./src/models/*"], "@/config/*": ["./src/config/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"], "ts-node": {"esm": true}}