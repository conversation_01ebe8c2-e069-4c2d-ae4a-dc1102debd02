{"name": "reposensei-backend", "version": "1.0.0", "description": "RepoSensei Backend API - AI-powered GitHub profile analysis", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate": "tsx src/database/migrate.ts", "seed": "tsx src/database/seed.ts", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.11.0", "winston": "^3.11.0", "redis": "^4.6.10", "bull": "^4.12.2", "@supabase/supabase-js": "^2.38.4", "pg": "^8.11.3", "axios": "^1.6.2", "@huggingface/inference": "^2.6.4", "sharp": "^0.32.6", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.10.4", "@types/pg": "^8.10.9", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "tsx": "^4.6.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "nodemon": "^3.0.2"}, "keywords": ["github", "ai", "anime", "profile-analysis", "typescript", "express", "api"], "author": "HectorTa1989", "license": "MIT", "engines": {"node": ">=18.0.0"}}