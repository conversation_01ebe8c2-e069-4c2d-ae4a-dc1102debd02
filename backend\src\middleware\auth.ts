import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { supabase } from '@/config/database';
import { logger, logError, logSecurityEvent } from '@/utils/logger';
import { redis } from '@/config/redis';

// Extend Express Request type
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: string;
        iat: number;
        exp: number;
      };
    }
  }
}

interface JWTPayload {
  id: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

class AuthMiddleware {
  private jwtSecret: string;

  constructor() {
    this.jwtSecret = process.env.JWT_SECRET!;
    
    if (!this.jwtSecret) {
      throw new Error('JWT_SECRET environment variable is required');
    }
  }

  /**
   * Verify JWT token and authenticate user
   */
  public authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
          success: false,
          error: 'Authentication token required'
        });
        return;
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix

      // Check if token is blacklisted
      const isBlacklisted = await this.isTokenBlacklisted(token);
      if (isBlacklisted) {
        logSecurityEvent('blacklisted_token_used', { token: token.substring(0, 20) + '...' });
        res.status(401).json({
          success: false,
          error: 'Token has been revoked'
        });
        return;
      }

      // Verify JWT token
      const decoded = jwt.verify(token, this.jwtSecret) as JWTPayload;

      // Additional validation with Supabase
      const { data: user, error } = await supabase.auth.getUser(token);
      
      if (error || !user.user) {
        logSecurityEvent('invalid_token_used', { 
          error: error?.message,
          token: token.substring(0, 20) + '...'
        });
        res.status(401).json({
          success: false,
          error: 'Invalid or expired token'
        });
        return;
      }

      // Attach user info to request
      req.user = {
        id: decoded.id,
        email: decoded.email,
        role: decoded.role || 'user',
        iat: decoded.iat,
        exp: decoded.exp
      };

      // Update last activity
      await this.updateLastActivity(decoded.id);

      next();

    } catch (error) {
      logError('Authentication failed', error as Error, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      if (error instanceof jwt.JsonWebTokenError) {
        res.status(401).json({
          success: false,
          error: 'Invalid token format'
        });
        return;
      }

      if (error instanceof jwt.TokenExpiredError) {
        res.status(401).json({
          success: false,
          error: 'Token has expired'
        });
        return;
      }

      res.status(500).json({
        success: false,
        error: 'Authentication service unavailable'
      });
    }
  };

  /**
   * Optional authentication - doesn't fail if no token provided
   */
  public optionalAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // No token provided, continue without authentication
      next();
      return;
    }

    // Token provided, try to authenticate
    await this.authenticate(req, res, next);
  };

  /**
   * Require specific role
   */
  public requireRole = (requiredRole: string) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      if (req.user.role !== requiredRole && req.user.role !== 'admin') {
        logSecurityEvent('unauthorized_role_access', {
          userId: req.user.id,
          requiredRole,
          userRole: req.user.role,
          endpoint: req.path
        });

        res.status(403).json({
          success: false,
          error: 'Insufficient permissions'
        });
        return;
      }

      next();
    };
  };

  /**
   * Admin only access
   */
  public requireAdmin = (req: Request, res: Response, next: NextFunction): void => {
    this.requireRole('admin')(req, res, next);
  };

  /**
   * Check if token is blacklisted
   */
  private async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const tokenHash = this.hashToken(token);
      return await redis.exists(`blacklist:${tokenHash}`);
    } catch (error) {
      logError('Error checking token blacklist', error as Error);
      return false;
    }
  }

  /**
   * Blacklist a token
   */
  public async blacklistToken(token: string, expiresAt?: number): Promise<void> {
    try {
      const tokenHash = this.hashToken(token);
      const ttl = expiresAt ? Math.max(0, expiresAt - Math.floor(Date.now() / 1000)) : 86400; // 24 hours default
      
      await redis.set(`blacklist:${tokenHash}`, 'true', ttl);
      
      logger.info('Token blacklisted', { tokenHash });
    } catch (error) {
      logError('Error blacklisting token', error as Error);
    }
  }

  /**
   * Update user's last activity timestamp
   */
  private async updateLastActivity(userId: string): Promise<void> {
    try {
      const timestamp = new Date().toISOString();
      await redis.set(`activity:${userId}`, timestamp, 86400); // 24 hours
    } catch (error) {
      logError('Error updating last activity', error as Error, { userId });
    }
  }

  /**
   * Hash token for blacklist storage
   */
  private hashToken(token: string): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Generate JWT token
   */
  public generateToken(payload: { id: string; email: string; role?: string }): string {
    return jwt.sign(
      {
        id: payload.id,
        email: payload.email,
        role: payload.role || 'user'
      },
      this.jwtSecret,
      {
        expiresIn: process.env.JWT_EXPIRES_IN || '7d',
        issuer: 'reposensei',
        audience: 'reposensei-users'
      }
    );
  }

  /**
   * Verify and decode token without authentication
   */
  public verifyToken(token: string): JWTPayload | null {
    try {
      return jwt.verify(token, this.jwtSecret) as JWTPayload;
    } catch (error) {
      return null;
    }
  }

  /**
   * Rate limiting by user ID
   */
  public rateLimitByUser = (maxRequests: number, windowMs: number) => {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      if (!req.user) {
        next();
        return;
      }

      const key = `rate_limit:user:${req.user.id}`;
      const current = await redis.incr(key);
      
      if (current === 1) {
        await redis.expire(key, Math.ceil(windowMs / 1000));
      }

      if (current > maxRequests) {
        logSecurityEvent('rate_limit_exceeded', {
          userId: req.user.id,
          endpoint: req.path,
          attempts: current
        });

        res.status(429).json({
          success: false,
          error: 'Rate limit exceeded',
          retryAfter: windowMs / 1000
        });
        return;
      }

      // Add rate limit headers
      res.set({
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': Math.max(0, maxRequests - current).toString(),
        'X-RateLimit-Reset': new Date(Date.now() + windowMs).toISOString()
      });

      next();
    };
  };

  /**
   * Validate API key for service-to-service communication
   */
  public validateApiKey = (req: Request, res: Response, next: NextFunction): void => {
    const apiKey = req.headers['x-api-key'] as string;
    const validApiKey = process.env.API_KEY;

    if (!validApiKey) {
      res.status(500).json({
        success: false,
        error: 'API key validation not configured'
      });
      return;
    }

    if (!apiKey || apiKey !== validApiKey) {
      logSecurityEvent('invalid_api_key', {
        providedKey: apiKey ? apiKey.substring(0, 8) + '...' : 'none',
        ip: req.ip
      });

      res.status(401).json({
        success: false,
        error: 'Invalid API key'
      });
      return;
    }

    next();
  };
}

// Export singleton instance
export const authMiddleware = new AuthMiddleware();
export default authMiddleware;
